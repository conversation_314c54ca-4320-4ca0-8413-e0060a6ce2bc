import 'package:admin_dashboard/Models/question.dart';
import 'package:admin_dashboard/Models/quiz.dart';

// New model for quiz answers summary from the API
class QuizAnswersSummary {
  final List<QuizAnswerSummaryItem> data;

  QuizAnswersSummary({
    required this.data,
  });

  factory QuizAnswersSummary.fromJson(Map<String, dynamic> json) {
    return QuizAnswersSummary(
      data: (json['data'] as List)
          .map((item) => QuizAnswerSummaryItem.fromJson(item))
          .toList(),
    );
  }
}

class QuizAnswerSummaryItem {
  final int id;
  final String studentName;
  final String studentCode;
  final String quizName;
  final int finalGrade;
  final int? grade;
  final int timeTaken;
  final bool autoGraded;
  final DateTime createdAt;

  QuizAnswerSummaryItem({
    required this.id,
    required this.studentName,
    required this.studentCode,
    required this.quizName,
    required this.finalGrade,
    this.grade,
    required this.timeTaken,
    required this.autoGraded,
    required this.createdAt,
  });

  factory QuizAnswerSummaryItem.fromJson(Map<String, dynamic> json) {
    return QuizAnswerSummaryItem(
      id: json['id'],
      studentName: json['studentName'],
      studentCode: json['studentCode'],
      quizName: json['quizName'],
      finalGrade: json['finalGrade'],
      grade: json['grade'],
      timeTaken: json['timeTaken'],
      autoGraded: json['autoGraded'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  // Helper method to format time taken
  String get formattedTimeTaken {
    final minutes = timeTaken ~/ 60;
    final seconds = timeTaken % 60;
    return '${minutes}m ${seconds}s';
  }
}

class QuizAnswersList {
  final int quizId;
  final String quizName;
  final int totalSubmissions;
  final List<QuizAnswerSummary> answers;

  QuizAnswersList({
    required this.quizId,
    required this.quizName,
    required this.totalSubmissions,
    required this.answers,
  });

  factory QuizAnswersList.fromJson(Map<String, dynamic> json) {
    try {
      print('QuizAnswersList.fromJson input: $json');

      // Check if answers is a list and not empty
      var answersList = <QuizAnswerSummary>[];
      if (json['answers'] != null) {
        print('Answers type: ${json['answers'].runtimeType}');
        if (json['answers'] is List) {
          answersList = (json['answers'] as List)
              .map((answer) => QuizAnswerSummary.fromJson(answer))
              .toList();
          print('Parsed ${answersList.length} answers');
        } else {
          print('Answers is not a List: ${json['answers']}');
        }
      } else {
        print('Answers is null');
      }

      return QuizAnswersList(
        quizId: json['quizId'] ?? 0,
        quizName: json['quizName'] ?? '',
        totalSubmissions: json['totalSubmissions'] ?? 0,
        answers: answersList,
      );
    } catch (e) {
      print('Error in QuizAnswersList.fromJson: $e');
      // Return a default object in case of parsing error
      return QuizAnswersList(
        quizId: 0,
        quizName: '',
        totalSubmissions: 0,
        answers: [],
      );
    }
  }
}

class QuizAnswerSummary {
  final int id;
  final String studentName;
  final String studentCode;
  final int attemptNumber;
  final DateTime submissionDate;
  final int? grade;

  QuizAnswerSummary({
    required this.id,
    required this.studentName,
    required this.studentCode,
    required this.attemptNumber,
    required this.submissionDate,
    this.grade,
  });

  factory QuizAnswerSummary.fromJson(Map<String, dynamic> json) {
    try {
      print('QuizAnswerSummary.fromJson input: $json');
      return QuizAnswerSummary(
        id: json['id'] ?? 0,
        studentName: json['studentName'] ?? '',
        studentCode: json['studentCode'] ?? '',
        attemptNumber: json['attemptNumber'] ?? 0,
        submissionDate: json['submissionDate'] != null
            ? DateTime.parse(json['submissionDate'])
            : DateTime.now(),
        grade: json['grade'],  // Already nullable, no need for special handling
      );
    } catch (e) {
      print('Error in QuizAnswerSummary.fromJson: $e');
      // Return a default object in case of parsing error
      return QuizAnswerSummary(
        id: 0,
        studentName: 'Unknown',
        studentCode: 'Unknown',
        attemptNumber: 0,
        submissionDate: DateTime.now(),
        grade: null,
      );
    }
  }
}

class StudentAnswer {
  final String type;
  final int questionId;
  final int? selectedAnswerId;
  final String? text;
  final String? recordingUrl;

  StudentAnswer({
    required this.type,
    required this.questionId,
    this.selectedAnswerId,
    this.text,
    this.recordingUrl,
  });

  factory StudentAnswer.fromJson(Map<String, dynamic> json) {
    return StudentAnswer(
      type: json['type'],
      questionId: json['questionId'],
      selectedAnswerId: json['selectedAnswerId'],
      text: json['text'],
      recordingUrl: json['recordingUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'type': type,
      'questionId': questionId,
    };

    if (selectedAnswerId != null) data['selectedAnswerId'] = selectedAnswerId;
    if (text != null) data['text'] = text;
    if (recordingUrl != null) data['recordingUrl'] = recordingUrl;

    return data;
  }
}

class CombinedQuestionAnswer {
  final Question question;
  final StudentAnswer studentAnswer;

  CombinedQuestionAnswer({
    required this.question,
    required this.studentAnswer,
  });

  factory CombinedQuestionAnswer.fromJson(Map<String, dynamic> json) {
    return CombinedQuestionAnswer(
      question: Question.fromJson(json['question']),
      studentAnswer: StudentAnswer.fromJson(json['studentAnswer']),
    );
  }
}

class QuizAnswer {
  final int id;
  final int studentId;
  final int quizId;
  final List<StudentAnswer> answers;
  final int? grade;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? quiz;
  final Map<String, dynamic>? student;
  final List<CombinedQuestionAnswer>? combinedData;

  QuizAnswer({
    required this.id,
    required this.studentId,
    required this.quizId,
    required this.answers,
    this.grade,
    required this.createdAt,
    required this.updatedAt,
    this.quiz,
    this.student,
    this.combinedData,
  });

  factory QuizAnswer.fromJson(Map<String, dynamic> json) {
    List<StudentAnswer> answersList = [];
    if (json['answers'] != null) {
      answersList = (json['answers'] as List)
          .map((answer) => StudentAnswer.fromJson(answer))
          .toList();
    }

    List<CombinedQuestionAnswer>? combinedDataList;
    if (json['combinedData'] != null) {
      combinedDataList = (json['combinedData'] as List)
          .map((data) => CombinedQuestionAnswer.fromJson(data))
          .toList();
    }

    return QuizAnswer(
      id: json['id'],
      studentId: json['studentId'],
      quizId: json['quizId'],
      answers: answersList,
      grade: json['grade'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      quiz: json['quiz'],
      student: json['student'],
      combinedData: combinedDataList,
    );
  }
}
