{"buildFiles": ["E:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\ma3had al7an\\admin_dashboard\\android\\app\\.cxx\\Debug\\1n2h152g\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\ma3had al7an\\admin_dashboard\\android\\app\\.cxx\\Debug\\1n2h152g\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}