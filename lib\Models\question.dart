enum QuestionType { mcq, text, record }

class MCQAnswer {
  final int id;
  final String text;

  MCQAnswer({required this.id, required this.text});

  Map<String, dynamic> toJson() => {
    'id': id,
    'text': text,
  };
}

class Question {
  final String question;
  final QuestionType type;
  final double grade;
  final List<MCQAnswer>? answers;
  final int? correctAnswerId;
  final String? correctAnswer;
  final int? maxDuration;

  Question({
    required this.question,
    required this.type,
    required this.grade,
    this.answers,
    this.correctAnswerId,
    this.correctAnswer,
    this.maxDuration,
  }) {
    if (type == QuestionType.mcq && (answers == null || correctAnswerId == null)) {
      throw ArgumentError('MCQ questions must have answers and a correct answer');
    }
  }

  Map<String, dynamic> toJson() => {
    'question': question,
    'type': type.toString().split('.').last,
    'grade': grade,
    if (answers != null) 'answers': answers!.map((a) => a.toJson()).toList(),
    if (correctAnswerId != null) 'correctAnswerId': correctAnswerId,
    if (correctAnswer != null) 'correctAnswer': correctAnswer,
    if (maxDuration != null) 'maxDuration': maxDuration,
  };

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      question: json['question'],
      type: QuestionType.values.firstWhere(
            (e) => e.toString().split('.').last == json['type'],
      ),
      grade: json['grade'].toDouble(),
      answers: json['answers'] != null
          ? (json['answers'] as List)
          .map((a) => MCQAnswer(id: a['id'], text: a['text']))
          .toList()
          : null,
      correctAnswerId: json['correctAnswerId'],
      correctAnswer: json['correctAnswer'],
      maxDuration: json['maxDuration'],
    );
  }
}