class QuizGrade {
  final String name;
  final int? userGrade;
  final int finalGrade;
  final String type;
  final DateTime? submissionDate;

  QuizGrade({
    required this.name,
    this.userGrade,
    required this.finalGrade,
    required this.type,
    this.submissionDate,
  });

  factory QuizGrade.fromJson(Map<String, dynamic> json) {
    return QuizGrade(
      name: json['name'],
      userGrade: json['userGrade'],
      finalGrade: json['finalGrade'],
      type: json['type'] ?? 'Unknown',
      submissionDate: json['submissionDate'] != null 
          ? DateTime.parse(json['submissionDate']) 
          : null,
    );
  }
}